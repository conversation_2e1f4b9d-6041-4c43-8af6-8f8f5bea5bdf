# Sohnus - 多语言求职平台

## 项目简介

Sohnus 是一个连接求职者和雇主的多语言平台，支持英语、德语、中文和西班牙语。该平台使用 AI 技术帮助求职者创建简历并匹配合适的工作机会。

**项目URL**: https://lovable.dev/projects/194f53bb-7db8-4dc0-b0de-c11a25114b18

## 🌍 国际化 (i18n) 系统

### 支持的语言
- **英语 (en)** 🇺🇸 - 默认语言
- **德语 (de)** 🇩🇪 - 完整翻译
- **中文 (zh)** 🇨🇳 - 完整翻译
- **西班牙语 (es)** 🇪🇸 - 完整翻译

### 系统架构

#### 1. 语言文件结构
```
public/locales/
├── en.json    # 英语翻译
├── de.json    # 德语翻译
├── zh.json    # 中文翻译
└── es.json    # 西班牙语翻译
```

#### 2. 核心组件
- **I18nContext** (`src/contexts/I18nContext.tsx`) - 语言管理上下文
- **LanguageSwitcher** (`src/components/LanguageSwitcher.tsx`) - 语言切换器
- **useI18n Hook** - 翻译钩子函数
- **useFormOptions Hook** - 表单选项钩子函数

### 工作原理

#### 语言检测与默认设置
1. **默认语言**: 始终为英语 (en)
2. **用户选择**: 通过右上角下拉菜单切换语言
3. **持久化**: 语言选择保存在 localStorage 中
4. **动态加载**: 语言文件按需动态加载

#### 数据标准化系统
这是系统的核心特性 - **无论用户使用哪种语言提交表单，数据库中存储的都是标准化的英文键值**：

**示例：雇佣类型字段**
- 用户看到的显示值：
  - 英语: "Full-time"
  - 德语: "Vollzeit"
  - 中文: "全职"
  - 西班牙语: "Tiempo Completo"
- 数据库存储的标准值: `"full_time"`

**所有标准化字段：**
```javascript
// 雇佣类型
EmploymentType: "full_time" | "part_time" | "contract" | "temporary" | "internship"

// 教育水平
EducationLevel: "no_degree" | "vocational_training" | "high_school" | "bachelors_degree" | "masters_degree" | "phd"

// 德语水平
GermanLevel: "a1" | "a2" | "b1" | "b2" | "c1" | "c2" | "native_speaker"

// 驾照类型
DrivingLicense: "none" | "class_b" | "class_c1" | "class_ce" | "forklift_license"

// 紧急程度
Urgency: "immediate" | "urgent" | "normal" | "flexible"

// 语言标识
Language: "en" | "de" | "zh" | "es"
```

#### 文化适配
- **中文用户**: 姓名字段顺序为 [姓] [名]（符合中文习惯）
- **其他语言**: 姓名字段顺序为 [名] [姓]（符合西方习惯）

#### 法律页面多语言支持
- **德语用户**: 链接到德语版隐私政策和服务条款
- **其他语言**: 链接到英语版页面

### 使用方法

#### 在组件中使用翻译
```typescript
import { useI18n } from '@/contexts/I18nContext';

const MyComponent = () => {
  const { t, language } = useI18n();

  return (
    <div>
      <h1>{t('hero.title')}</h1>
      <p>{t('hero.subtitle')}</p>
    </div>
  );
};
```

#### 使用表单选项
```typescript
import { useFormOptions } from '@/contexts/I18nContext';

const FormComponent = () => {
  const formOptions = useFormOptions();

  return (
    <select>
      {formOptions.employmentTypes.map((option) => (
        <option key={option.key} value={option.key}>
          {option.label}
        </option>
      ))}
    </select>
  );
};
```

### 数据库优势

#### 查询简化
```sql
-- 查询所有全职工作者（无论提交语言）
SELECT * FROM workers WHERE EmploymentType = 'full_time';

-- 查询所有紧急招聘需求
SELECT * FROM companies WHERE Urgency = 'immediate';
```

#### 数据分析
- 统一的数据格式便于生成报告
- 跨语言数据聚合变得简单
- 避免了多语言数据混乱问题

### 添加新语言

1. **创建语言文件**: `public/locales/[语言代码].json`
2. **更新类型定义**: 在 `I18nContext.tsx` 中添加语言代码
3. **更新语言切换器**: 在 `LanguageSwitcher.tsx` 中添加新选项
4. **翻译所有键值**: 确保完整翻译所有文本

### 后端集成

#### NocoDB 配置
所有下拉字段必须配置为接受标准化键值：
- EmploymentType: `full_time, part_time, contract, temporary, internship`
- EducationLevel: `no_degree, vocational_training, high_school, bachelors_degree, masters_degree, phd`
- 等等...

#### 表单提交
每个表单提交都包含 `Language` 字段，记录用户使用的语言，便于后续分析和客户服务。

## 如何编辑代码？

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/194f53bb-7db8-4dc0-b0de-c11a25114b18) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## 技术栈

### 前端技术
- **Vite** - 构建工具
- **TypeScript** - 类型安全的 JavaScript
- **React** - UI 框架
- **shadcn-ui** - UI 组件库
- **Tailwind CSS** - CSS 框架
- **React Router** - 路由管理

### 国际化技术
- **自定义 i18n 系统** - 基于 React Context
- **动态语言加载** - JSON 文件按需加载
- **语言持久化** - localStorage 存储用户偏好

### 后端集成
- **NocoDB** - 数据库管理（托管在 Google Cloud 德国法兰克福）
- **Brevo** - 邮件通知服务
- **Netlify Functions** - 无服务器函数

### 数据存储
- **标准化数据模型** - 语言无关的键值存储
- **GDPR 合规** - 欧盟数据保护法规遵循

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/194f53bb-7db8-4dc0-b0de-c11a25114b18) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
